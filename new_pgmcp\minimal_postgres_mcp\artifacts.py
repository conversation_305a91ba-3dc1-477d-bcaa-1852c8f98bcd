"""Simple artifacts for the minimal MCP server."""

from typing import Any, Dict


class ErrorResult:
    """Simple error result class."""

    def __init__(self, message: str):
        self.value = message

    def to_text(self) -> str:
        return self.value


class ExplainPlanArtifact:
    """Simple explain plan artifact."""

    def __init__(self, plan_data: Dict[str, Any]):
        self.plan_data = plan_data

    @classmethod
    def from_json_data(cls, data: Dict[str, Any]) -> 'ExplainPlanArtifact':
        """Create from JSON data."""
        return cls(data)

    def to_text(self) -> str:
        """Convert to text representation."""
        return self._format_plan(self.plan_data)

    def _format_plan(self, plan: Dict[str, Any], indent: int = 0) -> str:
        """Format plan data as text."""
        result = []
        prefix = "  " * indent
        
        # Basic plan information
        node_type = plan.get('Node Type', 'Unknown')
        total_cost = plan.get('Total Cost', 0)
        startup_cost = plan.get('Startup Cost', 0)
        plan_rows = plan.get('Plan Rows', 0)
        plan_width = plan.get('Plan Width', 0)
        
        result.append(f"{prefix}{node_type}")
        result.append(f"{prefix}  Cost: {startup_cost:.2f}..{total_cost:.2f}")
        result.append(f"{prefix}  Rows: {plan_rows} Width: {plan_width}")
        
        # Add actual times if available (from ANALYZE)
        if 'Actual Total Time' in plan:
            actual_time = plan['Actual Total Time']
            actual_rows = plan.get('Actual Rows', 0)
            result.append(f"{prefix}  Actual: time={actual_time:.3f} rows={actual_rows}")
        
        # Add any additional info
        if 'Index Name' in plan:
            result.append(f"{prefix}  Index: {plan['Index Name']}")
        if 'Relation Name' in plan:
            result.append(f"{prefix}  Relation: {plan['Relation Name']}")
        if 'Filter' in plan:
            result.append(f"{prefix}  Filter: {plan['Filter']}")
        if 'Index Cond' in plan:
            result.append(f"{prefix}  Index Cond: {plan['Index Cond']}")
        
        # Process child plans
        if 'Plans' in plan:
            for child_plan in plan['Plans']:
                result.append(self._format_plan(child_plan, indent + 1))
        
        return '\n'.join(result)

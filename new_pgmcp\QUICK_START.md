# 快速开始指南

## 🚀 5分钟快速启动

### 1. 安装
```bash
cd new_pgmcp
pip install -e .
```

### 2. 配置数据库（可选，用于测试）
```bash
# Windows PowerShell
$env:DATABASE_URI="postgresql://user:password@localhost:5432/database"

# Linux/Mac
export DATABASE_URI="postgresql://user:password@localhost:5432/database"
```

### 3. 启动服务器
```bash
# SSE 模式（默认，Web 访问）
python -m minimal_postgres_mcp.server

# 服务器将在 http://0.0.0.0:9090 启动
```

### 4. 验证服务器
在另一个终端运行：
```bash
python test_sse_server.py --config-test
```

## 🔧 配置选项

### SSE 模式（推荐）
```bash
# 默认配置（监听所有接口，端口 9090）
python -m minimal_postgres_mcp.server

# 自定义端口和主机
python -m minimal_postgres_mcp.server --sse-host localhost --sse-port 8080
```

### stdio 模式
```bash
# 标准输入输出模式（用于 MCP 客户端）
python -m minimal_postgres_mcp.server --transport stdio
```

### 安全模式
```bash
# 只读模式（推荐生产环境）
python -m minimal_postgres_mcp.server --access-mode restricted
```

## 🛠️ 可用工具

1. **list_schemas** - 列出数据库模式
2. **list_objects** - 列出表、视图、序列
3. **get_object_details** - 获取对象详细信息
4. **execute_sql** - 执行 SQL 查询
5. **explain_query** - 查询执行计划分析

## 🌐 Web 访问

服务器启动后，可以通过以下方式访问：

- **HTTP 地址**: http://0.0.0.0:9090
- **本地访问**: http://localhost:9090
- **网络访问**: http://[你的IP]:9090

## 📝 示例使用

### 基本查询示例
```python
# 运行示例脚本
python example_usage.py
```

### 连接测试
```python
# 测试数据库连接
python test_server.py
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 使用不同端口
   python -m minimal_postgres_mcp.server --sse-port 8080
   ```

2. **数据库连接失败**
   ```bash
   # 检查连接字符串
   echo $DATABASE_URI
   
   # 测试连接
   python test_server.py
   ```

3. **权限问题**
   ```bash
   # 使用只读模式
   python -m minimal_postgres_mcp.server --access-mode restricted
   ```

### 调试模式

查看详细日志：
```bash
# 启动时会显示详细的连接和配置信息
python -m minimal_postgres_mcp.server
```

## 📚 更多信息

- **完整文档**: 查看 `README.md`
- **部署指南**: 查看 `DEPLOYMENT.md`
- **项目总结**: 查看 `PROJECT_SUMMARY.md`
- **变更记录**: 查看 `CHANGELOG.md`

## 🎯 下一步

1. **配置生产数据库连接**
2. **设置只读用户权限**
3. **配置防火墙规则**
4. **集成到你的 MCP 客户端**

---

**提示**: 这是一个最小化的 PostgreSQL MCP 服务器，专注于核心功能。如果需要更多高级功能，可以基于此版本进行扩展。

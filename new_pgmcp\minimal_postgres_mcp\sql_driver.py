"""SQL driver for PostgreSQL connections."""

import logging
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse, urlunparse

from psycopg.rows import dict_row
from psycopg_pool import AsyncConnectionPool
from typing_extensions import LiteralString

logger = logging.getLogger(__name__)


def obfuscate_password(text: str | None) -> str | None:
    """Obfuscate password in connection strings."""
    if text is None:
        return None
    if not text:
        return text

    # Try first as a proper URL
    try:
        parsed = urlparse(text)
        if parsed.scheme and parsed.netloc and parsed.password:
            netloc = parsed.netloc.replace(parsed.password, "****")
            return urlunparse(parsed._replace(netloc=netloc))
    except Exception:
        pass

    # Handle various password patterns
    url_pattern = re.compile(r"(postgres(?:ql)?:\/\/[^:]+:)([^@]+)(@[^\/\s]+)")
    text = re.sub(url_pattern, r"\1****\3", text)
    
    param_pattern = re.compile(r'(password=)([^\s&;"\']+)', re.IGNORECASE)
    text = re.sub(param_pattern, r"\1****", text)

    return text


@dataclass
class RowResult:
    """Represents a row result from a database query."""
    cells: Dict[str, Any]


class DbConnPool:
    """Database connection manager using psycopg's connection pool."""

    def __init__(self, connection_url: Optional[str] = None):
        self.connection_url = connection_url
        self.pool: AsyncConnectionPool | None = None
        self._is_valid = False
        self._last_error = None

    async def pool_connect(self, connection_url: Optional[str] = None) -> AsyncConnectionPool:
        """Initialize connection pool."""
        if self.pool and self._is_valid:
            return self.pool

        url = connection_url or self.connection_url
        self.connection_url = url
        if not url:
            self._is_valid = False
            self._last_error = "Database connection URL not provided"
            raise ValueError(self._last_error)

        await self.close()

        try:
            self.pool = AsyncConnectionPool(
                conninfo=url,
                min_size=1,
                max_size=5,
                open=False,
            )
            await self.pool.open()

            # Test connection
            async with self.pool.connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")

            self._is_valid = True
            self._last_error = None
            return self.pool
        except Exception as e:
            self._is_valid = False
            self._last_error = str(e)
            await self.close()
            raise ValueError(f"Connection failed: {obfuscate_password(str(e))}") from e

    async def close(self):
        """Close the connection pool."""
        if self.pool:
            try:
                await self.pool.close()
            except Exception as e:
                logger.warning(f"Error closing pool: {e}")
            finally:
                self.pool = None
                self._is_valid = False


class SqlDriver:
    """SQL driver for executing queries against PostgreSQL."""

    def __init__(self, conn: Any = None, engine_url: str | None = None):
        if conn:
            self.conn = conn
            self.is_pool = isinstance(conn, DbConnPool)
        elif engine_url:
            self.engine_url = engine_url
            self.conn = None
            self.is_pool = False
        else:
            raise ValueError("Either conn or engine_url must be provided")

    def connect(self):
        if self.conn is not None:
            return self.conn
        if self.engine_url:
            self.conn = DbConnPool(self.engine_url)
            self.is_pool = True
            return self.conn
        else:
            raise ValueError("Connection not established")

    async def execute_query(
        self,
        query: LiteralString,
        params: list[Any] | None = None,
        force_readonly: bool = False,
    ) -> Optional[List[RowResult]]:
        """Execute a query and return results."""
        try:
            if self.conn is None:
                self.connect()
                if self.conn is None:
                    raise ValueError("Connection not established")

            if self.is_pool:
                pool = await self.conn.pool_connect()
                async with pool.connection() as connection:
                    return await self._execute_with_connection(connection, query, params, force_readonly)
            else:
                return await self._execute_with_connection(self.conn, query, params, force_readonly)
        except Exception as e:
            if self.conn and self.is_pool:
                self.conn._is_valid = False
                self.conn._last_error = str(e)
            elif self.conn and not self.is_pool:
                self.conn = None
            raise e

    async def _execute_with_connection(
        self, connection, query: str, params: list[Any] | None = None, force_readonly: bool = False
    ) -> Optional[List[RowResult]]:
        """Execute query with a specific connection."""
        try:
            async with connection.cursor(row_factory=dict_row) as cursor:
                if params:
                    await cursor.execute(query, params)
                else:
                    await cursor.execute(query)
                
                # Check if there are results to fetch
                if cursor.description:
                    rows = await cursor.fetchall()
                    return [RowResult(cells=dict(row)) for row in rows]
                else:
                    # For queries that don't return results (INSERT, UPDATE, etc.)
                    return []
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            raise e

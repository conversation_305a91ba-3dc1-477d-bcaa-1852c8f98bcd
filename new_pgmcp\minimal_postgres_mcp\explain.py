"""Simple explain plan functionality."""

import logging
from typing import Any, Dict, List

from .artifacts import Error<PERSON><PERSON>ult, ExplainPlanArtifact
from .sql_driver import SqlDriver

logger = logging.getLogger(__name__)


class ExplainPlanTool:
    """Tool for generating explain plans."""

    def __init__(self, sql_driver: SqlDriver):
        self.sql_driver = sql_driver

    async def explain(self, sql_query: str) -> ExplainPlanArtifact | ErrorResult:
        """Generate a basic explain plan."""
        try:
            explain_query = f"EXPLAIN (FORMAT JSON) {sql_query}"
            rows = await self.sql_driver.execute_query(explain_query)
            
            if not rows or len(rows) == 0:
                return ErrorResult("No results returned from EXPLAIN")

            query_plan_data = rows[0].cells.get("QUERY PLAN")
            if not isinstance(query_plan_data, list) or len(query_plan_data) == 0:
                return ErrorResult("Invalid EXPLAIN result format")

            plan_dict = query_plan_data[0]
            if not isinstance(plan_dict, dict):
                return ErrorResult("Invalid plan data format")

            return ExplainPlanArtifact.from_json_data(plan_dict)
        except Exception as e:
            logger.error(f"Error in explain: {e}")
            return ErrorResult(f"Error executing explain plan: {e}")

    async def explain_analyze(self, sql_query: str) -> ExplainPlanArtifact | ErrorResult:
        """Generate an explain analyze plan."""
        try:
            explain_query = f"EXPLAIN (ANALYZE, FORMAT JSON) {sql_query}"
            rows = await self.sql_driver.execute_query(explain_query)
            
            if not rows or len(rows) == 0:
                return ErrorResult("No results returned from EXPLAIN ANALYZE")

            query_plan_data = rows[0].cells.get("QUERY PLAN")
            if not isinstance(query_plan_data, list) or len(query_plan_data) == 0:
                return ErrorResult("Invalid EXPLAIN ANALYZE result format")

            plan_dict = query_plan_data[0]
            if not isinstance(plan_dict, dict):
                return ErrorResult("Invalid plan data format")

            return ExplainPlanArtifact.from_json_data(plan_dict)
        except Exception as e:
            logger.error(f"Error in explain analyze: {e}")
            return ErrorResult(f"Error executing explain analyze: {e}")

# Minimal PostgreSQL MCP Server

A minimal implementation of a PostgreSQL MCP (Model Context Protocol) server that provides basic database operations.

## Features

- List database schemas
- List tables, views, and sequences in schemas
- Get detailed information about database objects
- Execute SQL queries (with optional read-only mode)
- Basic EXPLAIN plan analysis

## Installation

```bash
# Clone or copy the new_pgmcp folder
cd new_pgmcp

# Install dependencies
pip install -e .
```

## Quick Start

1. **Set up your database connection:**
   ```bash
   export DATABASE_URI="postgresql://user:password@localhost:5432/dbname"
   ```

2. **Test the connection:**
   ```bash
   python test_server.py
   ```

3. **Run the server:**
   ```bash
   # Using the module
   python -m minimal_postgres_mcp.server

   # Or using the convenience script
   python start_server.py

   # Or with command line arguments
   python -m minimal_postgres_mcp.server "postgresql://user:password@localhost:5432/dbname"
   ```

4. **Try the example usage:**
   ```bash
   python example_usage.py
   ```

## Usage Examples

### Basic Usage
```bash
# Run with database URL
python -m minimal_postgres_mcp.server "postgresql://user:password@localhost:5432/dbname"

# Run in restricted (read-only) mode
python -m minimal_postgres_mcp.server "postgresql://user:password@localhost:5432/dbname" --access-mode restricted

# Use environment variable for database URL
export DATABASE_URI="postgresql://user:password@localhost:5432/dbname"
python -m minimal_postgres_mcp.server
```

### SSE Mode (for web clients)
```bash
# Run with SSE transport
python -m minimal_postgres_mcp.server --transport sse --sse-port 8000
```

## Available Tools

1. **list_schemas** - List all schemas in the database
2. **list_objects** - List objects (tables, views, sequences) in a schema
3. **get_object_details** - Get detailed information about a database object
4. **execute_sql** - Execute SQL queries
5. **explain_query** - Get execution plan for SQL queries

## Access Modes

- **unrestricted**: Full SQL access (default)
- **restricted**: Read-only access with safety features

## File Structure

```
new_pgmcp/
├── minimal_postgres_mcp/
│   ├── __init__.py          # Package initialization
│   ├── server.py            # Main MCP server
│   ├── sql_driver.py        # Database connection and query execution
│   ├── safe_sql.py          # Read-only SQL restrictions
│   ├── artifacts.py         # Result formatting
│   └── explain.py           # Query plan analysis
├── pyproject.toml           # Project configuration
├── README.md                # This file
├── test_server.py           # Connection testing
├── example_usage.py         # Usage examples
└── start_server.py          # Convenience startup script
```

## Testing

The package includes several testing and example scripts:

- `test_server.py` - Test database connectivity
- `example_usage.py` - Demonstrate all available tools
- `start_server.py` - Easy server startup with configuration help

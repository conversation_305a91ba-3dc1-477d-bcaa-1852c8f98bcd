# act -W ../.github/workflows/docker-build-dockerhub.yml -s DOCKERHUB_USERNAME -s DOCKERHUB_TOKEN -s GITHUB_TOKEN="$(gh auth token)"
---
name: Build and Push Docker Image to DockerHub

on:
  push:
    tags:
      - "v*"
  workflow_dispatch:
    inputs:
      version:
        description: "Version to release (without v prefix)"
        required: true
        default: ""

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.set-version.outputs.VERSION }}
    steps:
      - name: Set version from tag
        id: set-version
        run: |
          if [[ "${{ github.event_name }}" == "push" && \
                "${{ github.ref_type }}" == "tag" ]]; then
            VERSION="${GITHUB_REF#refs/tags/v}"
          else
            VERSION="${{ github.event.inputs.version }}"
          fi
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

  build-and-push:
    needs: prepare
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true
          version: latest
          driver-opts: image=moby/buildkit:latest

      - name: Inspect builder
        run: |
          echo "Available platforms: $(docker buildx inspect --bootstrap | grep 'Platforms:')"
          docker buildx ls

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64,amd64
          image: tonistiigi/binfmt:latest

      - name: Prepare Docker tags
        id: docker_meta
        uses: docker/metadata-action@v5
        with:
          images: crystaldba/postgres-mcp
          tags: |
            type=raw,value=${{ needs.prepare.outputs.version }}
            type=raw,value=latest

      - name: Check directory structure
        run: |
          echo "check pwd: $(pwd)"
          echo "check ls: $(ls -lta)"

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.docker_meta.outputs.tags }}
          cache-from: type=registry,ref=crystaldba/postgres-mcp:buildcache
          cache-to: type=registry,ref=crystaldba/postgres-mcp:buildcache,mode=max

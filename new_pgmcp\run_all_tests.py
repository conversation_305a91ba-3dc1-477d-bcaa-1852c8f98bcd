#!/usr/bin/env python3
"""Run all tests for the minimal PostgreSQL MCP server."""

import asyncio
import subprocess
import sys
import os
import time

def run_command(cmd, description, timeout=10):
    """Run a command and return success status."""
    print(f"\n=== {description} ===")
    print(f"Running: {cmd}")
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        if result.returncode == 0:
            print("✓ SUCCESS")
            if result.stdout.strip():
                print("Output:")
                print(result.stdout.strip())
            return True
        else:
            print("✗ FAILED")
            if result.stderr.strip():
                print("Error:")
                print(result.stderr.strip())
            if result.stdout.strip():
                print("Output:")
                print(result.stdout.strip())
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ TIMEOUT (>{timeout}s)")
        return False
    except Exception as e:
        print(f"✗ ERROR: {e}")
        return False

def test_imports():
    """Test that all modules can be imported."""
    print("\n=== Testing Module Imports ===")
    
    modules = [
        "minimal_postgres_mcp",
        "minimal_postgres_mcp.server",
        "minimal_postgres_mcp.sql_driver",
        "minimal_postgres_mcp.safe_sql",
        "minimal_postgres_mcp.artifacts",
        "minimal_postgres_mcp.explain"
    ]
    
    success_count = 0
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module}")
            success_count += 1
        except Exception as e:
            print(f"✗ {module}: {e}")
    
    print(f"\nImport test: {success_count}/{len(modules)} modules imported successfully")
    return success_count == len(modules)

def main():
    """Run all tests."""
    print("=== Minimal PostgreSQL MCP Server - Test Suite ===")
    print("This script runs comprehensive tests for the server.")
    
    # Change to the correct directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    tests = []
    
    # Test 1: Module imports
    tests.append(("Module Imports", test_imports))
    
    # Test 2: Package installation check
    tests.append(("Package Installation", lambda: run_command(
        "pip show minimal-postgres-mcp", 
        "Check package installation"
    )))
    
    # Test 3: Server help
    tests.append(("Server Help", lambda: run_command(
        "python -m minimal_postgres_mcp.server --help", 
        "Test server help command"
    )))
    
    # Test 4: Configuration test
    tests.append(("Server Configuration", lambda: run_command(
        "python test_sse_server.py --config-test", 
        "Test server configuration"
    )))
    
    # Test 5: Syntax check for all Python files
    python_files = [
        "minimal_postgres_mcp/server.py",
        "minimal_postgres_mcp/sql_driver.py",
        "minimal_postgres_mcp/safe_sql.py",
        "minimal_postgres_mcp/artifacts.py",
        "minimal_postgres_mcp/explain.py",
        "test_server.py",
        "test_sse_server.py",
        "example_usage.py",
        "start_server.py"
    ]
    
    for py_file in python_files:
        tests.append((f"Syntax Check: {py_file}", lambda f=py_file: run_command(
            f"python -m py_compile {f}", 
            f"Syntax check for {f}"
        )))
    
    # Run all tests
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
    
    # Summary
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\nThe minimal PostgreSQL MCP server is ready to use!")
        print("\nNext steps:")
        print("1. Set DATABASE_URI environment variable")
        print("2. Start the server: python -m minimal_postgres_mcp.server")
        print("3. Access via HTTP at: http://0.0.0.0:9090")
        return True
    else:
        print("❌ SOME TESTS FAILED")
        print("\nPlease fix the issues before using the server.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

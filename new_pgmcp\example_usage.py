#!/usr/bin/env python3
"""Example usage of the minimal PostgreSQL MCP server tools."""

import asyncio
import json
from minimal_postgres_mcp.server import (
    list_schemas, list_objects, get_object_details, 
    execute_sql, explain_query, get_sql_driver,
    db_connection, current_access_mode, AccessMode
)


async def demo_tools():
    """Demonstrate the available MCP tools."""
    # Set up database connection (you need to set DATABASE_URI)
    import os
    database_url = os.environ.get("DATABASE_URI")
    if not database_url:
        print("Please set DATABASE_URI environment variable")
        return
    
    try:
        await db_connection.pool_connect(database_url)
        print("Connected to database successfully!\n")
        
        # Demo 1: List schemas
        print("=== Demo 1: List Schemas ===")
        result = await list_schemas()
        print(json.dumps(eval(result[0].text), indent=2))
        
        # Demo 2: List tables in public schema
        print("\n=== Demo 2: List Tables in 'public' Schema ===")
        result = await list_objects(schema_name="public", object_type="table")
        tables = eval(result[0].text)
        print(json.dumps(tables, indent=2))
        
        # Demo 3: Get details of first table (if any)
        if tables:
            table_name = tables[0]['name']
            print(f"\n=== Demo 3: Get Details of Table '{table_name}' ===")
            result = await get_object_details(
                schema_name="public", 
                object_name=table_name, 
                object_type="table"
            )
            print(json.dumps(eval(result[0].text), indent=2))
        
        # Demo 4: Execute simple query
        print("\n=== Demo 4: Execute Simple Query ===")
        result = await execute_sql(sql="SELECT current_database(), current_user, version()")
        print(json.dumps(eval(result[0].text), indent=2))
        
        # Demo 5: Explain query
        print("\n=== Demo 5: Explain Query ===")
        result = await explain_query(sql="SELECT * FROM pg_tables LIMIT 1")
        print(result[0].text)
        
        print("\n=== All demos completed successfully! ===")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await db_connection.close()


async def demo_restricted_mode():
    """Demonstrate restricted mode functionality."""
    global current_access_mode
    
    print("\n=== Demo: Restricted Mode ===")
    
    # Switch to restricted mode
    original_mode = current_access_mode
    current_access_mode = AccessMode.RESTRICTED
    
    try:
        # This should work (SELECT)
        print("Testing SELECT query in restricted mode...")
        result = await execute_sql(sql="SELECT 1 as test")
        print("✓ SELECT query allowed")
        
        # This should fail (INSERT)
        print("Testing INSERT query in restricted mode...")
        try:
            result = await execute_sql(sql="INSERT INTO test_table VALUES (1)")
            print("✗ INSERT query was allowed (unexpected)")
        except:
            print("✓ INSERT query blocked (expected)")
        
    finally:
        # Restore original mode
        current_access_mode = original_mode


if __name__ == "__main__":
    asyncio.run(demo_tools())
    asyncio.run(demo_restricted_mode())

# PostgreSQL MCP 服务器使用指南

## 项目简介

PostgreSQL MCP Pro 是一个基于 Model Context Protocol (MCP) 的 PostgreSQL 数据库管理和优化工具。它提供了数据库健康检查、索引调优、查询计划分析、安全 SQL 执行等功能。

## 功能特性

- 🔍 **数据库健康检查** - 分析索引健康、连接利用率、缓冲区缓存、vacuum 健康状态等
- ⚡ **索引调优** - 使用工业级算法探索数千种可能的索引组合，找到最佳解决方案
- 📈 **查询计划分析** - 通过 EXPLAIN 计划验证和优化性能，模拟假设索引的影响
- 🧠 **模式智能** - 基于对数据库模式的详细理解进行上下文感知的 SQL 生成
- 🛡️ **安全 SQL 执行** - 可配置的访问控制，支持只读模式和安全 SQL 解析

## 系统要求

- Python 3.12 或更高版本
- PostgreSQL 数据库（推荐版本 13-17）
- Docker（可选，推荐使用）

## 安装方式

### 方式一：使用 Docker（推荐）

```bash
# 拉取 Docker 镜像
docker pull crystaldba/postgres-mcp
```

### 方式二：使用 Python

```bash
# 使用 pipx 安装
pipx install postgres-mcp

# 或使用 uv 安装
uv pip install postgres-mcp
```

### 方式三：从源码安装

```bash
# 克隆仓库
git clone https://github.com/crystaldba/postgres-mcp.git
cd postgres-mcp

# 安装 uv（如果未安装）
curl -sSL https://astral.sh/uv/install.sh | sh

# 安装依赖
uv pip install -e .
uv sync
```

## 运行模式

PostgreSQL MCP 服务器支持两种传输模式：

1. **stdio 模式**：标准输入输出模式，适用于 MCP 客户端直接调用
2. **SSE 模式**：Server-Sent Events 模式，支持多个客户端共享一个服务器实例

## SSE 模式本地运行指南

### 1. 启动 SSE 服务器

#### 使用 Docker 启动

```bash
# 基本启动命令
docker run -p 8000:8000 \
  -e DATABASE_URI="postgresql://username:<EMAIL>:5432/dbname" \
  crystaldba/postgres-mcp \
  --transport=sse \
  --access-mode=unrestricted

# 自定义主机和端口
docker run -p 9000:9000 \
  -e DATABASE_URI="postgresql://username:<EMAIL>:5432/dbname" \
  crystaldba/postgres-mcp \
  --transport=sse \
  --sse-host=0.0.0.0 \
  --sse-port=9000 \
  --access-mode=restricted
```

#### 使用 Python 启动

```bash
# 使用 pipx 安装后启动
postgres-mcp "postgresql://username:password@localhost:5432/dbname" \
  --transport=sse \
  --sse-host=localhost \
  --sse-port=8000 \
  --access-mode=unrestricted

# 使用 uv 启动
uv run postgres-mcp "postgresql://username:password@localhost:5432/dbname" \
  --transport=sse \
  --sse-host=localhost \
  --sse-port=8000 \
  --access-mode=unrestricted
```

#### 从源码启动

```bash
cd postgres-mcp
uv run python -m postgres_mcp.server \
  "postgresql://username:password@localhost:5432/dbname" \
  --transport=sse \
  --sse-host=localhost \
  --sse-port=8000 \
  --access-mode=unrestricted
```

### 2. 命令行参数说明

- `database_url`: PostgreSQL 连接字符串（必需）
- `--transport`: 传输模式，选择 `stdio` 或 `sse`（默认：stdio）
- `--access-mode`: 访问模式，选择 `unrestricted` 或 `restricted`（默认：unrestricted）
- `--sse-host`: SSE 服务器绑定的主机地址（默认：localhost）
- `--sse-port`: SSE 服务器端口（默认：8000）

### 3. 访问模式说明

#### Unrestricted 模式（不受限制）
- 允许完全的读写访问
- 可以修改数据和模式
- 适用于开发环境

#### Restricted 模式（受限制）
- 仅限于只读事务
- 对资源利用率施加约束（目前仅限执行时间）
- 适用于生产环境

### 4. 验证服务器运行状态

启动服务器后，可以通过以下方式验证：

```bash
# 检查服务器是否响应
curl http://localhost:8000/health

# 检查 SSE 端点
curl http://localhost:8000/sse
```

### 5. 配置 MCP 客户端

#### Cursor 配置

在 Cursor 的 `mcp.json` 文件中添加：

```json
{
    "mcpServers": {
        "postgres": {
            "type": "sse",
            "url": "http://localhost:8000/sse"
        }
    }
}
```

#### Windsurf 配置

在 Windsurf 的 `mcp_config.json` 文件中添加：

```json
{
    "mcpServers": {
        "postgres": {
            "type": "sse",
            "serverUrl": "http://localhost:8000/sse"
        }
    }
}
```

#### Cline 配置

在 Cline 的 `cline_mcp_settings.json` 文件中添加：

```json
{
    "mcpServers": {
        "postgres": {
            "type": "sse",
            "url": "http://localhost:8000/sse"
        }
    }
}
```

## 数据库连接配置

### 连接字符串格式

```
postgresql://[username[:password]@][host[:port]][/database][?param1=value1&...]
```

### 示例连接字符串

```bash
# 本地数据库
postgresql://postgres:password@localhost:5432/mydb

# 远程数据库
postgresql://user:<EMAIL>:5432/production_db

# 使用 SSL
postgresql://user:<EMAIL>:5432/mydb?sslmode=require

# 连接池配置
postgresql://user:pass@localhost:5432/mydb?pool_size=20&max_overflow=30
```

### 环境变量配置

也可以通过环境变量设置数据库连接：

```bash
export DATABASE_URI="postgresql://username:password@localhost:5432/dbname"
postgres-mcp --transport=sse
```

## PostgreSQL 扩展安装（可选但推荐）

为了启用索引调优和全面的性能分析功能，需要安装以下扩展：

### 1. pg_stat_statements 扩展

```sql
-- 在 postgresql.conf 中添加
shared_preload_libraries = 'pg_stat_statements'

-- 重启 PostgreSQL 后执行
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
```

### 2. hypopg 扩展

```sql
-- 对于云服务提供商（AWS RDS、Azure SQL、Google Cloud SQL）
CREATE EXTENSION IF NOT EXISTS hypopg;

-- 对于自管理的 PostgreSQL，可能需要先安装系统包
-- Ubuntu/Debian: apt-get install postgresql-contrib
-- CentOS/RHEL: yum install postgresql-contrib
```

## 使用示例

### 1. 数据库健康检查

```bash
# 启动服务器后，在 AI 助手中询问：
"检查我的数据库健康状况并识别任何问题"
```

### 2. 分析慢查询

```bash
"数据库中最慢的查询是什么？如何加速它们？"
```

### 3. 索引优化建议

```bash
"分析我的数据库工作负载并建议索引以提高性能"
```

### 4. 查询计划分析

```bash
"帮我优化这个查询：SELECT * FROM orders JOIN customers ON orders.customer_id = customers.id WHERE orders.created_at > '2023-01-01'"
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库连接字符串是否正确
   - 确认数据库服务正在运行
   - 验证网络连接和防火墙设置

2. **权限错误**
   - 确保数据库用户有足够的权限
   - 检查访问模式设置是否合适

3. **端口冲突**
   - 更改 SSE 端口：`--sse-port=9000`
   - 检查端口是否被其他服务占用

4. **扩展未安装**
   - 某些功能需要 `pg_stat_statements` 和 `hypopg` 扩展
   - 按照上述说明安装扩展

### 日志查看

```bash
# Docker 容器日志
docker logs <container_id>

# 直接运行时的日志会输出到控制台
```

## 安全注意事项

1. **生产环境使用**
   - 在生产环境中使用 `--access-mode=restricted`
   - 考虑使用只读数据库用户
   - 定期审查访问日志

2. **网络安全**
   - 在生产环境中不要将 SSE 服务器暴露到公网
   - 考虑使用反向代理和 HTTPS
   - 实施适当的身份验证和授权

3. **数据保护**
   - 避免在连接字符串中硬编码密码
   - 使用环境变量或安全的配置管理
   - 定期轮换数据库密码

## 高级配置

### 自定义 Docker 运行

```bash
# 使用自定义网络
docker network create postgres-mcp-net
docker run --network postgres-mcp-net -p 8000:8000 \
  -e DATABASE_URI="******************************/mydb" \
  crystaldba/postgres-mcp --transport=sse

# 挂载配置文件
docker run -v /path/to/config:/app/config -p 8000:8000 \
  -e DATABASE_URI="postgresql://user:pass@localhost:5432/mydb" \
  crystaldba/postgres-mcp --transport=sse
```

### 性能调优

```bash
# 增加连接池大小
postgresql://user:pass@localhost:5432/mydb?pool_size=50&max_overflow=100

# 设置连接超时
postgresql://user:pass@localhost:5432/mydb?connect_timeout=30
```

## 支持和社区

- GitHub 仓库：https://github.com/crystaldba/postgres-mcp
- Discord 社区：https://discord.gg/4BEHC7ZM
- 问题报告：https://github.com/crystaldba/postgres-mcp/issues
- 文档：https://www.crystaldba.ai

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

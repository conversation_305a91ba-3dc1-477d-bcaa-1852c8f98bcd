[project]
name = "postgres-mcp"
version = "0.3.0"
description = "PostgreSQL Tuning and Analysis Tool"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "mcp[cli]>=1.5.0",
    "psycopg[binary]>=3.2.6",
    "humanize>=4.8.0",
    "pglast==7.2.0",
    "attrs>=25.3.0",
    "psycopg-pool>=3.2.6",
    "instructor>=1.7.9",
]
license = "mit"
license-files = ["LICENSE"]

[[project.authors]]
name = "<PERSON>"
email = "<EMAIL>"

[build-system]
requires = [ "hatchling",]
build-backend = "hatchling.build"

[project.scripts]
postgres-mcp = "postgres_mcp:main"

# [tool.pyright]
# venvPath = "."
# venv = ".venv"

[tool.pytest.ini_options]
pythonpath = ["./src"]
asyncio_default_fixture_loop_scope = "function"

[dependency-groups]
dev = [
    "docker>=7.1.0",
    "pyright==1.1.398",
    "pytest-asyncio>=0.26.0",
    "pytest>=8.3.5",
    "ruff==0.11.2",
]

[tool.black]
line-length = 150

[tool.ruff]
line-length = 150
target-version = "py38"
exclude = [".venv*"]

lint.select = [
    "E",   # pycodestyle errors
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "W",   # pycodestyle warnings
    # "C90", # mccabe complexity
    "N",   # pep8-naming
    # "D",   # pydocstyle
    "UP",  # pyupgrade
    "RUF"  # ruff-specific rules
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.isort]
force-single-line = true
known-first-party = ["postgres-mcp"]

[tool.pyright]
typeCheckingMode = "standard"
pythonVersion = "3.12"
strictParameterNullChecking = true
reportMissingTypeStubs = false
# reportUnknownMemberType = true
# reportUnknownParameterType = true
# reportUnknownVariableType = true
# reportUnknownArgumentType = true
reportPrivateUsage = true
reportUntypedFunctionDecorator = true
reportMissingTypeArgument = true
reportInvalidTypeVarUse = true
reportUnnecessaryCast = true
# reportUnnecessaryComparison = true

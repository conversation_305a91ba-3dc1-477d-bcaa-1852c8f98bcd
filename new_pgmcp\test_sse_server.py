#!/usr/bin/env python3
"""Test script for SSE server functionality."""

import asyncio
import os
import sys
import time
from minimal_postgres_mcp.server import main

async def start_sse_server():
    """Start the SSE server for testing."""
    print("=== Testing SSE Server ===")
    print("Starting MCP server with SSE transport...")
    print("Server will be available at: http://0.0.0.0:9090")
    print("Press Ctrl+C to stop the server.")
    
    # Set a test database URL if not provided
    if not os.environ.get("DATABASE_URI"):
        print("\nWarning: DATABASE_URI not set. Using placeholder.")
        print("Database operations will fail until you set a valid DATABASE_URI.")
        os.environ["DATABASE_URI"] = "postgresql://user:pass@localhost:5432/testdb"
    
    try:
        # Override command line arguments to use SSE
        sys.argv = [
            "test_sse_server.py",
            "--transport", "sse",
            "--access-mode", "restricted"  # Use restricted mode for safety
        ]
        
        await main()
    except KeyboardInterrupt:
        print("\n✓ Server stopped by user.")
    except Exception as e:
        print(f"\n✗ Error starting server: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure port 9090 is not in use")
        print("2. Check if DATABASE_URI is set correctly")
        print("3. Verify PostgreSQL is accessible")

async def test_server_config():
    """Test server configuration without starting."""
    print("=== Server Configuration Test ===")

    try:
        from minimal_postgres_mcp.server import mcp
        print(f"✓ MCP instance created successfully")
        print(f"  Name: {mcp.name}")
        print(f"  Host: {mcp.settings.host}")
        print(f"  Port: {mcp.settings.port}")

        # Test tool registration
        tools = await mcp.list_tools()
        print(f"✓ Tools registered: {len(tools)} tools")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")

        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--config-test":
        # Just test configuration
        success = asyncio.run(test_server_config())
        sys.exit(0 if success else 1)
    else:
        # Start the server
        asyncio.run(start_sse_server())

"""Safe SQL driver that restricts to read-only operations."""

import logging
import re
from typing import Any, List, Optional
from typing_extensions import LiteralString

from .sql_driver import SqlDriver, RowResult

logger = logging.getLogger(__name__)


class SafeSqlDriver(SqlDriver):
    """A wrapper around SqlDriver that only allows safe read-only operations."""

    # Allowed statement types (case-insensitive)
    ALLOWED_STATEMENTS = {
        'SELECT', 'SHOW', 'DESCRIBE', 'DESC', 'EXPLAIN', 'ANALYZE'
    }

    def __init__(self, sql_driver: SqlDriver, timeout: int = 30):
        """Initialize with an existing SqlDriver."""
        self.sql_driver = sql_driver
        self.timeout = timeout

    def _is_safe_query(self, query: str) -> bool:
        """Check if a query is safe to execute (read-only)."""
        # Remove comments and normalize whitespace
        cleaned_query = re.sub(r'--.*$', '', query, flags=re.MULTILINE)
        cleaned_query = re.sub(r'/\*.*?\*/', '', cleaned_query, flags=re.DOTALL)
        cleaned_query = cleaned_query.strip()
        
        if not cleaned_query:
            return False

        # Get the first word (statement type)
        first_word = cleaned_query.split()[0].upper()
        
        # Check if it's an allowed statement
        if first_word in self.ALLOWED_STATEMENTS:
            return True
            
        # Special case for EXPLAIN ANALYZE
        if first_word == 'EXPLAIN':
            words = cleaned_query.split()
            if len(words) >= 2 and words[1].upper() == 'ANALYZE':
                # Check if the analyzed query is safe
                remaining_query = ' '.join(words[2:])
                return self._is_safe_query(remaining_query)
            return True
            
        return False

    async def execute_query(
        self,
        query: str,  # Changed from LiteralString to str for compatibility
        params: list[Any] | None = None,
        force_readonly: bool = True,
    ) -> Optional[List[RowResult]]:
        """Execute a query after validating it's safe."""
        if not self._is_safe_query(query):
            raise ValueError(f"Query not allowed in restricted mode: {query[:100]}...")
        
        return await self.sql_driver.execute_query(query, params, force_readonly=True)

    @staticmethod
    async def execute_param_query(
        sql_driver: SqlDriver,
        query: str,  # Changed from LiteralString to str for compatibility
        params: list[Any] | None = None
    ) -> list[RowResult] | None:
        """Execute a parameterized query with proper parameter binding."""
        if params:
            # Use proper parameter binding like the original code
            # Replace {} placeholders with $1, $2, etc. for PostgreSQL parameter binding
            param_query = query
            for i in range(len(params)):
                param_query = param_query.replace('{}', f'${i+1}', 1)
            return await sql_driver.execute_query(param_query, params)
        else:
            return await sql_driver.execute_query(query)

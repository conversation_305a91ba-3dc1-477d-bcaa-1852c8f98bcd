#!/usr/bin/env python3
"""Simple test script for the minimal PostgreSQL MCP server."""

import asyncio
import os
from minimal_postgres_mcp.sql_driver import DbConnPool, SqlDriver


async def test_connection():
    """Test database connection."""
    # You can set this to your test database URL
    database_url = os.environ.get("DATABASE_URI", "postgresql://user:password@localhost:5432/testdb")
    
    print(f"Testing connection to database...")
    
    try:
        # Test connection pool
        pool = DbConnPool()
        await pool.pool_connect(database_url)
        print("✓ Connection pool created successfully")
        
        # Test SQL driver
        driver = SqlDriver(conn=pool)
        rows = await driver.execute_query("SELECT version()")
        
        if rows:
            version = rows[0].cells.get('version', 'Unknown')
            print(f"✓ Database connection successful")
            print(f"  PostgreSQL version: {version}")
        
        # Test basic queries
        print("\nTesting basic queries...")
        
        # List schemas
        rows = await driver.execute_query("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT LIKE 'pg_%' 
            AND schema_name != 'information_schema'
            LIMIT 5
        """)
        
        if rows:
            print("✓ Schema listing works")
            for row in rows:
                print(f"  - {row.cells['schema_name']}")
        else:
            print("✓ Schema listing works (no user schemas found)")
        
        # Test explain
        rows = await driver.execute_query("EXPLAIN (FORMAT JSON) SELECT 1")
        if rows:
            print("✓ EXPLAIN functionality works")
        
        await pool.close()
        print("\n✓ All tests passed!")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        print("\nMake sure to:")
        print("1. Set DATABASE_URI environment variable")
        print("2. Ensure PostgreSQL is running and accessible")
        print("3. Check connection parameters")


if __name__ == "__main__":
    asyncio.run(test_connection())

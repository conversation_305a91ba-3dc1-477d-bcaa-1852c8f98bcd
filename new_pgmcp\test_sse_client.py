#!/usr/bin/env python3
"""Simple HTTP client to test SSE server functionality."""

import asyncio
import json
import aiohttp
import sys

async def test_sse_server():
    """Test the SSE server with HTTP requests."""
    base_url = "http://0.0.0.0:9090"
    
    print("=== Testing SSE Server ===")
    print(f"Testing server at: {base_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test 1: Check if server is running
            print("\n1. Testing server connectivity...")
            try:
                async with session.get(f"{base_url}/health") as response:
                    if response.status == 200:
                        print("✓ Server is running and accessible")
                    else:
                        print(f"⚠ Server responded with status: {response.status}")
            except Exception as e:
                print(f"✗ Cannot connect to server: {e}")
                print("Make sure the server is running with:")
                print("  python -m minimal_postgres_mcp.server")
                return False
            
            # Test 2: List available tools
            print("\n2. Testing MCP tools endpoint...")
            try:
                # This is a hypothetical endpoint - actual MCP over SSE might use different endpoints
                async with session.get(f"{base_url}/tools") as response:
                    if response.status == 200:
                        tools = await response.json()
                        print(f"✓ Found {len(tools)} tools")
                        for tool in tools:
                            print(f"  - {tool.get('name', 'Unknown')}")
                    else:
                        print(f"⚠ Tools endpoint returned status: {response.status}")
            except Exception as e:
                print(f"⚠ Tools endpoint test failed: {e}")
            
            # Test 3: Test basic HTTP response
            print("\n3. Testing basic HTTP response...")
            try:
                async with session.get(base_url) as response:
                    content = await response.text()
                    print(f"✓ Server responded with {len(content)} characters")
                    if "MCP" in content or "minimal-postgres" in content:
                        print("✓ Response contains expected content")
            except Exception as e:
                print(f"⚠ Basic HTTP test failed: {e}")
            
            return True
            
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

async def test_mcp_protocol():
    """Test MCP protocol over SSE (if supported)."""
    print("\n=== Testing MCP Protocol ===")
    print("Note: This is a basic connectivity test.")
    print("Full MCP protocol testing requires an MCP client.")
    
    # For now, just test that the server accepts connections
    base_url = "http://0.0.0.0:9090"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(base_url) as response:
                print(f"✓ Server accepts HTTP connections (status: {response.status})")
                return True
    except Exception as e:
        print(f"✗ MCP protocol test failed: {e}")
        return False

def print_usage():
    """Print usage instructions."""
    print("=== SSE Server Test Client ===")
    print()
    print("This script tests the SSE server functionality.")
    print()
    print("Before running this test:")
    print("1. Start the SSE server in another terminal:")
    print("   python -m minimal_postgres_mcp.server")
    print()
    print("2. Make sure the server is running at http://0.0.0.0:9090")
    print()
    print("3. Run this test:")
    print("   python test_sse_client.py")
    print()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h"]:
        print_usage()
        sys.exit(0)
    
    print("Starting SSE server tests...")
    print("Make sure the server is running first!")
    print()
    
    try:
        # Test server connectivity
        success1 = asyncio.run(test_sse_server())
        
        # Test MCP protocol basics
        success2 = asyncio.run(test_mcp_protocol())
        
        if success1 and success2:
            print("\n✓ All tests passed!")
            print("\nThe SSE server appears to be working correctly.")
            print("You can now use MCP clients to connect to: http://0.0.0.0:9090")
        else:
            print("\n⚠ Some tests failed.")
            print("Check the server logs for more information.")
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        sys.exit(1)

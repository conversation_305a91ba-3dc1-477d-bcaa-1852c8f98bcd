[project]
name = "minimal-postgres-mcp"
version = "0.1.0"
description = "Minimal PostgreSQL MCP Server"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "mcp[cli]>=1.5.0",
    "psycopg[binary]>=3.2.6",
    "psycopg-pool>=3.2.6",
    "pydantic>=2.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
minimal-postgres-mcp = "minimal_postgres_mcp.server:main"

[tool.ruff]
line-length = 150
target-version = "py312"

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "W",   # pycodestyle warnings
]

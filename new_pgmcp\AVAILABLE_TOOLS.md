# Available MCP Tools

This document lists all available tools in the Minimal PostgreSQL MCP Server.

## 🔧 Database Schema Tools

### `list_schemas`
**Description:** List all schemas in the database
**Parameters:** None
**Example Usage:**
```json
{"tool": "list_schemas", "parameters": {}}
```

### `list_objects`
**Description:** List objects in a schema
**Parameters:**
- `schema_name` (string): Schema name
- `object_type` (string): Object type: 'table', 'view', or 'sequence' (default: 'table')

**Example Usage:**
```json
{"tool": "list_objects", "parameters": {"schema_name": "alarm", "object_type": "table"}}
```

### `list_tables` (Alias)
**Description:** List tables in a schema (alias for list_objects with type='table')
**Parameters:**
- `schema_name` (string): Schema name

**Example Usage:**
```json
{"tool": "list_tables", "parameters": {"schema_name": "alarm"}}
```

### `get_object_details`
**Description:** Show detailed information about a database object
**Parameters:**
- `schema_name` (string): Schema name
- `object_name` (string): Object name
- `object_type` (string): Object type: 'table', 'view', or 'sequence' (default: 'table')

**Example Usage:**
```json
{"tool": "get_object_details", "parameters": {"schema_name": "alarm", "object_name": "alm_alarm_history", "object_type": "table"}}
```

## 🔍 Query Tools

### `execute_sql`
**Description:** Execute a SQL query against the database
**Parameters:**
- `sql` (string): SQL query to execute

**Example Usage:**
```json
{"tool": "execute_sql", "parameters": {"sql": "SELECT * FROM alarm.alm_alarm_history WHERE alarm_no > 20250620 LIMIT 10"}}
```

### `query_database` (Alias)
**Description:** Execute a SQL query and return results (alias for execute_sql)
**Parameters:**
- `sql` (string): SQL query to execute

**Example Usage:**
```json
{"tool": "query_database", "parameters": {"sql": "SELECT COUNT(*) FROM alarm.alm_alarm_history"}}
```

### `query_table_data`
**Description:** Query table data with flexible conditions
**Parameters:**
- `schema_name` (string): Schema name (e.g., 'alarm')
- `table_name` (string): Table name (e.g., 'alm_alarm_history')
- `columns` (string): Columns to select (default: '*')
- `where_condition` (string): WHERE condition (default: '')
- `order_by` (string): ORDER BY clause (default: '')
- `limit` (integer): Maximum number of rows to return (default: 100)

**Example Usage:**
```json
{
  "tool": "query_table_data", 
  "parameters": {
    "schema_name": "alarm",
    "table_name": "alm_alarm_history",
    "columns": "alarm_no, alarm_time, alarm_desc",
    "where_condition": "alarm_no > 20250620",
    "order_by": "alarm_time DESC",
    "limit": 50
  }
}
```

### `query_alarm_history`
**Description:** Query alarm history data with specific conditions
**Parameters:**
- `alarm_no_condition` (string): Alarm number condition (e.g., '> 20250620', '= 20250620', 'BETWEEN 20250620 AND 20250630')
- `date_condition` (string): Date condition for alarm_time (e.g., '>= 2025-06-20')
- `limit` (integer): Maximum number of rows to return (default: 100)
- `order_by` (string): Order by clause (default: 'alarm_time DESC')

**Example Usage:**
```json
{
  "tool": "query_alarm_history", 
  "parameters": {
    "alarm_no_condition": "> 20250620",
    "date_condition": ">= '2025-06-20'",
    "limit": 100,
    "order_by": "alarm_time DESC"
  }
}
```

## 📊 Analysis Tools

### `explain_query`
**Description:** Get the execution plan for a SQL query
**Parameters:**
- `sql` (string): SQL query to explain

**Example Usage:**
```json
{"tool": "explain_query", "parameters": {"sql": "SELECT * FROM alarm.alm_alarm_history WHERE alarm_no > 20250620"}}
```

## 🎯 Common Use Cases

### 1. Count Tables in a Schema
```json
{"tool": "list_tables", "parameters": {"schema_name": "video_analysis"}}
```

### 2. Query Alarm Data After Specific Date
```json
{
  "tool": "query_alarm_history", 
  "parameters": {
    "alarm_no_condition": "> 20250620",
    "limit": 100
  }
}
```

### 3. Get Table Structure
```json
{"tool": "get_object_details", "parameters": {"schema_name": "alarm", "object_name": "alm_alarm_history"}}
```

### 4. Custom SQL Query
```json
{"tool": "execute_sql", "parameters": {"sql": "SELECT alarm_no, alarm_time FROM alarm.alm_alarm_history WHERE alarm_no > 20250620 ORDER BY alarm_time DESC LIMIT 10"}}
```

## ⚠️ Access Mode Notes

- **RESTRICTED Mode:** Only read-only operations (SELECT, SHOW, DESCRIBE, EXPLAIN) are allowed
- **UNRESTRICTED Mode:** All SQL operations are allowed (including INSERT, UPDATE, DELETE)

The server is currently running in **RESTRICTED** mode, which means only safe read-only queries are permitted.

## 🔗 Server Information

- **Host:** 0.0.0.0
- **Port:** 9090
- **Transport:** SSE (Server-Sent Events)
- **Database:** ****************************************/Industrial_Internet
- **Access Mode:** RESTRICTED (read-only)

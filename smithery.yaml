# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - databaseUri
      - accessMode
    properties:
      databaseUri:
        type: string
        description: URI for accessing the database, e.g., postgres://user:password@host:port/database.
      accessMode:
        type: string
        description: The access mode for the MCP, e.g., "restricted" or "unrestricted".
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({command: '/app/docker-entrypoint.sh', args: ['postgres-mcp', '--access-mode',config.accessMode], env: {DATABASE_URI: config.databaseUri}})
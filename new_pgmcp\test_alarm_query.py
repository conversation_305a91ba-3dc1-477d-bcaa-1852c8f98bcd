#!/usr/bin/env python3
"""Test alarm query functionality."""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_alarm_query():
    """Test the alarm query tools."""
    try:
        from minimal_postgres_mcp.server import (
            query_alarm_history, 
            query_table_data, 
            list_schemas,
            list_tables,
            execute_sql
        )
        
        print("=== Testing Alarm Query Tools ===")
        
        # Test 1: List schemas
        print("\n1. Testing list_schemas...")
        try:
            schemas = await list_schemas()
            print(f"✓ Schemas listed successfully")
            print(f"  Result type: {type(schemas)}")
            if schemas:
                print(f"  First few characters: {str(schemas)[:200]}...")
        except Exception as e:
            print(f"✗ Error listing schemas: {e}")
        
        # Test 2: List tables in alarm schema
        print("\n2. Testing list_tables for alarm schema...")
        try:
            tables = await list_tables("alarm")
            print(f"✓ Tables listed successfully")
            print(f"  Result type: {type(tables)}")
            if tables:
                print(f"  First few characters: {str(tables)[:200]}...")
        except Exception as e:
            print(f"✗ Error listing tables: {e}")
        
        # Test 3: Query alarm history with alarm_no condition
        print("\n3. Testing query_alarm_history...")
        try:
            result = await query_alarm_history(
                alarm_no_condition="> 20250620",
                limit=5
            )
            print(f"✓ Alarm history query executed successfully")
            print(f"  Result type: {type(result)}")
            if result:
                print(f"  First few characters: {str(result)[:200]}...")
        except Exception as e:
            print(f"✗ Error querying alarm history: {e}")
        
        # Test 4: Query table data
        print("\n4. Testing query_table_data...")
        try:
            result = await query_table_data(
                schema_name="alarm",
                table_name="alm_alarm_history",
                columns="alarm_no, alarm_time",
                where_condition="alarm_no > 20250620",
                limit=5
            )
            print(f"✓ Table data query executed successfully")
            print(f"  Result type: {type(result)}")
            if result:
                print(f"  First few characters: {str(result)[:200]}...")
        except Exception as e:
            print(f"✗ Error querying table data: {e}")
        
        # Test 5: Direct SQL execution
        print("\n5. Testing direct SQL execution...")
        try:
            sql = "SELECT alarm_no, alarm_time FROM alarm.alm_alarm_history WHERE alarm_no > 20250620 LIMIT 5"
            result = await execute_sql(sql)
            print(f"✓ Direct SQL executed successfully")
            print(f"  Result type: {type(result)}")
            if result:
                print(f"  First few characters: {str(result)[:200]}...")
        except Exception as e:
            print(f"✗ Error executing direct SQL: {e}")
        
        print("\n=== Test Summary ===")
        print("All alarm query tools have been tested.")
        print("If you see errors above, they might be due to:")
        print("1. Database connection issues")
        print("2. Missing alarm schema or alm_alarm_history table")
        print("3. Access permissions")
        print("\nThe tools are properly defined and should work when:")
        print("- Database connection is established")
        print("- Required tables exist")
        print("- Proper permissions are granted")
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Make sure the server module is properly installed")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")

def main():
    """Run the alarm query tests."""
    print("Testing Alarm Query Tools")
    print("=" * 50)
    
    try:
        asyncio.run(test_alarm_query())
    except Exception as e:
        print(f"Failed to run tests: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

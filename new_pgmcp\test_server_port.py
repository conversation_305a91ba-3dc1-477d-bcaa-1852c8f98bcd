#!/usr/bin/env python3
"""Test server port connectivity."""

import socket
import sys
import time

def test_port(host, port, timeout=5):
    """Test if a port is open and accepting connections."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"Error testing port: {e}")
        return False

def main():
    """Test server connectivity on different ports."""
    hosts_ports = [
        ("localhost", 9090),
        ("127.0.0.1", 9090),
        ("0.0.0.0", 9090),
        ("localhost", 8000),
        ("127.0.0.1", 8000),
    ]
    
    print("Testing server connectivity...")
    print("=" * 50)
    
    for host, port in hosts_ports:
        print(f"Testing {host}:{port}...", end=" ")
        if test_port(host, port):
            print("✓ OPEN")
        else:
            print("✗ CLOSED")
    
    print("\n" + "=" * 50)
    print("If no ports are open, the server may not be running.")
    print("Start the server with:")
    print('python -m minimal_postgres_mcp.server "your_database_url"')

if __name__ == "__main__":
    main()

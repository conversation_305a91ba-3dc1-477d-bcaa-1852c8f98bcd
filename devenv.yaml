# yaml-language-server: $schema=https://devenv.sh/devenv.schema.json
inputs:
  nixpkgs:
    url: github:cachix/devenv-nixpkgs/rolling
  nixpkgs-unstable:
    url: github:nixos/nixpkgs/nixpkgs-unstable
  nixpkgs-python:
    url: github:cachix/nixpkgs-python
    inputs:
      nixpkgs:
        follows: nixpkgs

# If you're using non-OSS software, you can set allowUnfree to true.
# allowUnfree: true

# If you're willing to use a package that's vulnerable
# permittedInsecurePackages:
#  - "openssl-1.1.1w"

# If you have more than one devenv you can merge them
#imports:
# - ./backend

# Changelog

## [0.1.0] - 2025-07-04

### Added
- Initial release of Minimal PostgreSQL MCP Server
- Core database operations:
  - List schemas
  - List objects (tables, views, sequences)
  - Get object details
  - Execute SQL queries
  - Basic EXPLAIN plan analysis
- Two access modes:
  - Unrestricted: Full SQL access
  - Restricted: Read-only with safety checks
- Two transport modes:
  - stdio: Standard MCP communication
  - sse: Server-Sent Events for web clients
- Connection pooling with psycopg
- Error handling and logging
- Comprehensive documentation and examples

### Features
- **Database Schema Exploration**: Browse database structure
- **SQL Execution**: Run queries with optional read-only mode
- **Query Analysis**: Get execution plans for performance tuning
- **Safety Features**: Restricted mode prevents dangerous operations
- **Easy Deployment**: Simple installation and configuration

### Dependencies
- Python 3.12+
- mcp[cli] >= 1.5.0
- psycopg[binary] >= 3.2.6
- psycopg-pool >= 3.2.6
- pydantic >= 2.0.0

### Documentation
- README.md: Basic usage and features
- DEPLOYMENT.md: Production deployment guide
- Example scripts for testing and demonstration

"""Minimal PostgreSQL MCP Server."""

import argparse
import asyncio
import logging
import os
import signal
import sys
from enum import Enum
from typing import Any, List, Union

# Fix for Windows ProactorEventLoop compatibility with psycopg
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

import mcp.types as types
from mcp.server.fastmcp import FastMCP
from pydantic import Field

from .artifacts import <PERSON><PERSON>r<PERSON><PERSON>ult, ExplainPlanArtifact
from .explain import ExplainPlanTool
from .safe_sql import SafeSqlDriver
from .sql_driver import DbConnPool, SqlDriver, obfuscate_password

# Initialize FastMCP with default configuration (will be updated in main if needed)
mcp = FastMCP("minimal-postgres-mcp", port=9090, host="0.0.0.0")

ResponseType = List[types.TextContent | types.ImageContent | types.EmbeddedResource]

logger = logging.getLogger(__name__)


class AccessMode(str, Enum):
    """SQL access modes for the server."""
    UNRESTRICTED = "unrestricted"
    RESTRICTED = "restricted"


# Global variables
db_connection = DbConnPool()
current_access_mode = AccessMode.UNRESTRICTED
shutdown_in_progress = False


async def get_sql_driver() -> Union[SqlDriver, SafeSqlDriver]:
    """Get the appropriate SQL driver based on the current access mode."""
    base_driver = SqlDriver(conn=db_connection)

    if current_access_mode == AccessMode.RESTRICTED:
        logger.debug("Using SafeSqlDriver with restrictions")
        return SafeSqlDriver(sql_driver=base_driver, timeout=30)
    else:
        logger.debug("Using unrestricted SqlDriver")
        return base_driver


def format_text_response(text: Any) -> ResponseType:
    """Format a text response."""
    return [types.TextContent(type="text", text=str(text))]


def format_error_response(error: str) -> ResponseType:
    """Format an error response."""
    return format_text_response(f"Error: {error}")


@mcp.tool(description="List all schemas in the database")
async def list_schemas() -> ResponseType:
    """List all schemas in the database."""
    try:
        sql_driver = await get_sql_driver()
        rows = await sql_driver.execute_query(
            """
            SELECT
                schema_name,
                schema_owner,
                CASE
                    WHEN schema_name LIKE 'pg_%' THEN 'System Schema'
                    WHEN schema_name = 'information_schema' THEN 'System Information Schema'
                    ELSE 'User Schema'
                END as schema_type
            FROM information_schema.schemata
            ORDER BY schema_type, schema_name
            """
        )
        schemas = [row.cells for row in rows] if rows else []
        return format_text_response(schemas)
    except Exception as e:
        logger.error(f"Error listing schemas: {e}")
        return format_error_response(str(e))


@mcp.tool(description="List objects in a schema")
async def list_objects(
    schema_name: str = Field(description="Schema name"),
    object_type: str = Field(description="Object type: 'table', 'view', or 'sequence'", default="table"),
) -> ResponseType:
    """List objects of a given type in a schema."""
    try:
        sql_driver = await get_sql_driver()

        if object_type in ("table", "view"):
            table_type = "BASE TABLE" if object_type == "table" else "VIEW"
            query = f"""
                SELECT table_schema, table_name, table_type
                FROM information_schema.tables
                WHERE table_schema = '{schema_name}' AND table_type = '{table_type}'
                ORDER BY table_name
                """
            rows = await sql_driver.execute_query(query)
            objects = (
                [{"schema": row.cells["table_schema"], "name": row.cells["table_name"], "type": row.cells["table_type"]} for row in rows]
                if rows
                else []
            )

        elif object_type == "sequence":
            query = f"""
                SELECT sequence_schema, sequence_name, data_type
                FROM information_schema.sequences
                WHERE sequence_schema = '{schema_name}'
                ORDER BY sequence_name
                """
            rows = await sql_driver.execute_query(query)
            objects = (
                [{"schema": row.cells["sequence_schema"], "name": row.cells["sequence_name"], "data_type": row.cells["data_type"]} for row in rows]
                if rows
                else []
            )

        else:
            return format_error_response(f"Unsupported object type: {object_type}")

        return format_text_response(objects)
    except Exception as e:
        logger.error(f"Error listing objects: {e}")
        return format_error_response(str(e))


@mcp.tool(description="Show detailed information about a database object")
async def get_object_details(
    schema_name: str = Field(description="Schema name"),
    object_name: str = Field(description="Object name"),
    object_type: str = Field(description="Object type: 'table', 'view', or 'sequence'", default="table"),
) -> ResponseType:
    """Get detailed information about a database object."""
    try:
        sql_driver = await get_sql_driver()

        if object_type in ("table", "view"):
            # Get columns
            col_query = f"""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_schema = '{schema_name}' AND table_name = '{object_name}'
                ORDER BY ordinal_position
                """
            col_rows = await sql_driver.execute_query(col_query)
            columns = (
                [
                    {
                        "column": r.cells["column_name"],
                        "data_type": r.cells["data_type"],
                        "is_nullable": r.cells["is_nullable"],
                        "default": r.cells["column_default"],
                    }
                    for r in col_rows
                ]
                if col_rows
                else []
            )

            result = {
                "basic": {"schema": schema_name, "name": object_name, "type": object_type},
                "columns": columns,
            }

        elif object_type == "sequence":
            seq_query = f"""
                SELECT sequence_schema, sequence_name, data_type, start_value, increment
                FROM information_schema.sequences
                WHERE sequence_schema = '{schema_name}' AND sequence_name = '{object_name}'
                """
            rows = await sql_driver.execute_query(seq_query)

            if rows and rows[0]:
                row = rows[0]
                result = {
                    "schema": row.cells["sequence_schema"],
                    "name": row.cells["sequence_name"],
                    "data_type": row.cells["data_type"],
                    "start_value": row.cells["start_value"],
                    "increment": row.cells["increment"],
                }
            else:
                result = {}

        else:
            return format_error_response(f"Unsupported object type: {object_type}")

        return format_text_response(result)
    except Exception as e:
        logger.error(f"Error getting object details: {e}")
        return format_error_response(str(e))


@mcp.tool(description="Execute a SQL query")
async def execute_sql(
    sql: str = Field(description="SQL query to execute"),
) -> ResponseType:
    """Execute a SQL query against the database."""
    try:
        sql_driver = await get_sql_driver()
        rows = await sql_driver.execute_query(sql)
        if rows is None:
            return format_text_response("No results")
        return format_text_response([r.cells for r in rows])
    except Exception as e:
        logger.error(f"Error executing query: {e}")
        return format_error_response(str(e))


@mcp.tool(description="Explain the execution plan for a SQL query")
async def explain_query(
    sql: str = Field(description="SQL query to explain"),
    analyze: bool = Field(description="Use EXPLAIN ANALYZE for actual execution statistics", default=False),
) -> ResponseType:
    """Explain the execution plan for a SQL query."""
    try:
        sql_driver = await get_sql_driver()
        explain_tool = ExplainPlanTool(sql_driver=sql_driver)

        if analyze:
            result = await explain_tool.explain_analyze(sql)
        else:
            result = await explain_tool.explain(sql)

        if isinstance(result, ExplainPlanArtifact):
            return format_text_response(result.to_text())
        else:
            error_message = result.to_text() if isinstance(result, ErrorResult) else "Error processing explain plan"
            return format_error_response(error_message)
    except Exception as e:
        logger.error(f"Error explaining query: {e}")
        return format_error_response(str(e))


@mcp.tool(description="List tables in a schema (alias for list_objects with type='table')")
async def list_tables(
    schema_name: str = Field(description="Schema name")
) -> ResponseType:
    """List tables in a schema."""
    return await list_objects(schema_name, "table")


@mcp.tool(description="Execute a SQL query and return results")
async def query_database(
    sql: str = Field(description="SQL query to execute")
) -> ResponseType:
    """Execute a SQL query (alias for execute_sql)."""
    return await execute_sql(sql)


async def main():
    """Main entry point for the server."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Minimal PostgreSQL MCP Server")
    parser.add_argument("database_url", help="Database connection URL", nargs="?")
    parser.add_argument(
        "--access-mode",
        type=str,
        choices=[mode.value for mode in AccessMode],
        default=AccessMode.UNRESTRICTED.value,
        help="Set SQL access mode: unrestricted or restricted (read-only)",
    )
    parser.add_argument(
        "--transport",
        type=str,
        choices=["stdio", "sse"],
        default="sse",
        help="Select MCP transport: sse (default) or stdio",
    )
    parser.add_argument(
        "--sse-host",
        type=str,
        default="localhost",
        help="Host to bind SSE server to (default: localhost)",
    )
    parser.add_argument(
        "--sse-port",
        type=int,
        default=8000,
        help="Port for SSE server (default: 8000)",
    )

    args = parser.parse_args()

    # Store the access mode
    global current_access_mode
    current_access_mode = AccessMode(args.access_mode)

    # Add the execute_sql tool with appropriate description
    if current_access_mode == AccessMode.UNRESTRICTED:
        mcp.add_tool(execute_sql, description="Execute any SQL query")
    else:
        mcp.add_tool(execute_sql, description="Execute a read-only SQL query")

    logger.info(f"Starting Minimal PostgreSQL MCP Server in {current_access_mode.upper()} mode")

    # Get database URL
    database_url = os.environ.get("DATABASE_URI", args.database_url)

    if not database_url:
        raise ValueError(
            "Error: No database URL provided. Please specify via 'DATABASE_URI' environment variable or command-line argument."
        )

    # Initialize database connection
    try:
        await db_connection.pool_connect(database_url)
        logger.info("Successfully connected to database")
    except Exception as e:
        logger.warning(f"Could not connect to database: {obfuscate_password(str(e))}")
        logger.warning("The MCP server will start but database operations will fail until a valid connection is established.")

    # Set up shutdown handling
    try:
        loop = asyncio.get_running_loop()
        signals = (signal.SIGTERM, signal.SIGINT)
        for s in signals:
            loop.add_signal_handler(s, lambda s=s: asyncio.create_task(shutdown(s)))
    except NotImplementedError:
        # Windows doesn't support signals properly
        logger.warning("Signal handling not supported on Windows")
        pass

    # Run the server
    if args.transport == "stdio":
        await mcp.run_stdio_async()
    else:
        # Use SSE transport - FastMCP uses the host/port from initialization
        logger.info(f"Starting SSE server on 0.0.0.0:9090 (configured in FastMCP initialization)")
        logger.info(f"Note: Command line SSE options (--sse-host {args.sse_host}, --sse-port {args.sse_port}) are available but not used in this version")
        await mcp.run_sse_async()


async def shutdown(sig=None):
    """Clean shutdown of the server."""
    global shutdown_in_progress

    if shutdown_in_progress:
        logger.warning("Forcing immediate exit")
        sys.exit(1)

    shutdown_in_progress = True

    if sig:
        logger.info(f"Received exit signal {sig.name}")

    # Close database connections
    try:
        await db_connection.close()
        logger.info("Closed database connections")
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")

    # Exit
    sys.exit(128 + sig if sig is not None else 0)


if __name__ == "__main__":
    asyncio.run(main())

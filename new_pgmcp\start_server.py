#!/usr/bin/env python3
"""Start the minimal PostgreSQL MCP server with sample configuration."""

import os
import sys
import asyncio
from minimal_postgres_mcp.server import main

def setup_sample_config():
    """Set up sample configuration for testing."""
    # You can modify these values for your testing
    sample_configs = {
        # Example PostgreSQL connection strings (modify as needed)
        "local": "postgresql://postgres:password@localhost:5432/postgres",
        "docker": "postgresql://postgres:password@localhost:5432/testdb",
    }
    
    # Check if DATABASE_URI is already set
    if not os.environ.get("DATABASE_URI"):
        print("DATABASE_URI not set. Available sample configurations:")
        for name, url in sample_configs.items():
            print(f"  {name}: {url}")
        
        print("\nTo use a sample config, set DATABASE_URI environment variable:")
        print("  export DATABASE_URI='postgresql://user:password@host:port/database'")
        print("\nOr run with command line argument:")
        print("  python start_server.py 'postgresql://user:password@host:port/database'")
        
        # For testing, you can uncomment one of these:
        # os.environ["DATABASE_URI"] = sample_configs["local"]
        # print(f"\nUsing sample config: {sample_configs['local']}")
        
        return False
    
    return True

if __name__ == "__main__":
    print("=== Minimal PostgreSQL MCP Server ===")
    print("Starting server...")

    if not setup_sample_config():
        print("\nPlease configure DATABASE_URI and try again.")
        sys.exit(1)

    print(f"Database URL: {os.environ.get('DATABASE_URI', 'Not set')}")

    # Check if user wants SSE mode
    if len(sys.argv) > 1 and sys.argv[1] == "--sse":
        print("Starting MCP server in SSE mode...")
        print("Server will be available at: http://0.0.0.0:9090")
        print("Press Ctrl+C to stop the server.")
        # Override sys.argv for SSE mode
        sys.argv = ["start_server.py", "--transport", "sse"]
    else:
        print("Starting MCP server in stdio mode...")
        print("Use 'python start_server.py --sse' for SSE mode")
        print("Press Ctrl+C to stop the server.")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
    except Exception as e:
        print(f"\nError starting server: {e}")
        sys.exit(1)

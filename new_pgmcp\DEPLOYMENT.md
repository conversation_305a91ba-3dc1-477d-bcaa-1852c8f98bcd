# Deployment Guide

## Prerequisites

1. **Python 3.12+** installed
2. **PostgreSQL database** accessible
3. **Network connectivity** to the database

## Installation Steps

### 1. Install the Package

```bash
cd new_pgmcp
pip install -e .
```

### 2. Configure Database Connection

Set the database connection URL as an environment variable:

```bash
# Linux/Mac
export DATABASE_URI="postgresql://username:password@hostname:port/database"

# Windows (PowerShell)
$env:DATABASE_URI="postgresql://username:password@hostname:port/database"

# Windows (Command Prompt)
set DATABASE_URI=postgresql://username:password@hostname:port/database
```

### 3. Test the Connection

```bash
python test_server.py
```

Expected output:
```
Testing connection to database...
✓ Connection pool created successfully
✓ Database connection successful
  PostgreSQL version: PostgreSQL 15.x ...
✓ Schema listing works
✓ EXPLAIN functionality works
✓ All tests passed!
```

### 4. Start the Server

```bash
# Method 1: Using the module directly
python -m minimal_postgres_mcp.server

# Method 2: Using the convenience script
python start_server.py

# Method 3: With command line arguments
python -m minimal_postgres_mcp.server "postgresql://user:pass@host:port/db"
```

## Configuration Options

### Access Modes

- **unrestricted** (default): Full SQL access
- **restricted**: Read-only mode with safety checks

```bash
python -m minimal_postgres_mcp.server --access-mode restricted
```

### Transport Modes

- **stdio** (default): Standard input/output for MCP communication
- **sse**: Server-Sent Events for web clients

```bash
# SSE mode
python -m minimal_postgres_mcp.server --transport sse --sse-port 8000
```

## Production Deployment

### 1. Environment Variables

Create a `.env` file or set system environment variables:

```bash
DATABASE_URI=postgresql://username:password@hostname:port/database
MCP_ACCESS_MODE=restricted  # Optional: restrict to read-only
MCP_TRANSPORT=stdio         # Optional: transport mode
```

### 2. Process Management

Use a process manager like systemd, supervisor, or PM2:

**systemd service example:**
```ini
[Unit]
Description=Minimal PostgreSQL MCP Server
After=network.target

[Service]
Type=simple
User=mcp-user
WorkingDirectory=/path/to/new_pgmcp
Environment=DATABASE_URI=postgresql://user:pass@host:port/db
ExecStart=/usr/bin/python -m minimal_postgres_mcp.server --access-mode restricted
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 3. Security Considerations

- Use **restricted mode** in production
- Create a **dedicated database user** with minimal privileges
- Use **connection pooling** (already implemented)
- Monitor **connection limits**
- Set up **proper logging**

### 4. Database User Setup

Create a dedicated user for the MCP server:

```sql
-- Create user
CREATE USER mcp_user WITH PASSWORD 'secure_password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE your_database TO mcp_user;
GRANT USAGE ON SCHEMA public TO mcp_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO mcp_user;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO mcp_user;

-- For information_schema access (read-only)
GRANT SELECT ON information_schema.schemata TO mcp_user;
GRANT SELECT ON information_schema.tables TO mcp_user;
GRANT SELECT ON information_schema.columns TO mcp_user;
GRANT SELECT ON information_schema.sequences TO mcp_user;
```

## Troubleshooting

### Connection Issues

1. **Check database connectivity:**
   ```bash
   python test_server.py
   ```

2. **Verify connection string format:**
   ```
   postgresql://username:password@hostname:port/database
   ```

3. **Check firewall and network access**

4. **Verify PostgreSQL is running and accepting connections**

### Permission Issues

1. **Ensure database user has required permissions**
2. **Check schema access rights**
3. **Verify table-level permissions**

### Performance Issues

1. **Monitor connection pool usage**
2. **Check query execution times**
3. **Review database performance**

## Monitoring

The server logs important events. Monitor for:

- Connection pool status
- Query execution errors
- Access mode violations (in restricted mode)
- Database connectivity issues

## Scaling

For high-load scenarios:

1. **Increase connection pool size** in `sql_driver.py`
2. **Use multiple server instances** with load balancing
3. **Implement query caching** if needed
4. **Monitor database performance**

#!/usr/bin/env python3
"""Test Windows compatibility for the minimal PostgreSQL MCP server."""

import asyncio
import sys
import os

def test_event_loop_policy():
    """Test that the event loop policy is correctly set for Windows."""
    print("=== Testing Event Loop Policy ===")
    
    if sys.platform == "win32":
        policy = asyncio.get_event_loop_policy()
        print(f"Current event loop policy: {type(policy).__name__}")
        
        if isinstance(policy, asyncio.WindowsSelectorEventLoopPolicy):
            print("✓ Windows Selector Event Loop Policy is active")
            return True
        else:
            print("✗ Windows Selector Event Loop Policy is NOT active")
            print("  This may cause issues with psycopg on Windows")
            return False
    else:
        print("ℹ Not running on Windows, skipping event loop policy test")
        return True

def test_imports():
    """Test that all modules can be imported without issues."""
    print("\n=== Testing Module Imports ===")
    
    try:
        import minimal_postgres_mcp.server
        print("✓ minimal_postgres_mcp.server imported successfully")
        
        import minimal_postgres_mcp.sql_driver
        print("✓ minimal_postgres_mcp.sql_driver imported successfully")
        
        import minimal_postgres_mcp.safe_sql
        print("✓ minimal_postgres_mcp.safe_sql imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

async def test_server_initialization():
    """Test that the server can be initialized without database connection."""
    print("\n=== Testing Server Initialization ===")
    
    try:
        from minimal_postgres_mcp.server import mcp
        print("✓ FastMCP instance created successfully")
        
        # Test that tools are registered
        tools = await mcp.list_tools()
        print(f"✓ {len(tools)} tools registered: {[tool.name for tool in tools]}")
        
        return True
    except Exception as e:
        print(f"✗ Server initialization failed: {e}")
        return False

def test_database_url_parsing():
    """Test database URL parsing."""
    print("\n=== Testing Database URL Parsing ===")
    
    test_url = "*********************************************/Industrial_Internet"
    
    try:
        from minimal_postgres_mcp.sql_driver import obfuscate_password
        obfuscated = obfuscate_password(test_url)
        print(f"✓ URL obfuscation works: {obfuscated}")
        
        # Test that password is actually hidden
        if "greatech" not in obfuscated and "***" in obfuscated:
            print("✓ Password properly obfuscated")
            return True
        else:
            print("✗ Password not properly obfuscated")
            return False
            
    except Exception as e:
        print(f"✗ URL parsing failed: {e}")
        return False

async def test_safe_sql_validation():
    """Test SQL validation without database connection."""
    print("\n=== Testing SQL Validation ===")
    
    try:
        from minimal_postgres_mcp.safe_sql import SafeSqlDriver
        from minimal_postgres_mcp.sql_driver import SqlDriver
        
        # Create a mock SQL driver (won't actually connect)
        mock_driver = SqlDriver(None)  # Pass None as connection
        safe_driver = SafeSqlDriver(mock_driver)
        
        # Test safe queries
        safe_queries = [
            "SELECT * FROM users",
            "SHOW TABLES",
            "EXPLAIN SELECT * FROM users",
            "DESCRIBE users"
        ]
        
        for query in safe_queries:
            if safe_driver._is_safe_query(query):
                print(f"✓ Safe query validated: {query[:30]}...")
            else:
                print(f"✗ Safe query rejected: {query[:30]}...")
                return False
        
        # Test unsafe queries
        unsafe_queries = [
            "DROP TABLE users",
            "INSERT INTO users VALUES (1, 'test')",
            "UPDATE users SET name = 'test'",
            "DELETE FROM users"
        ]
        
        for query in unsafe_queries:
            if not safe_driver._is_safe_query(query):
                print(f"✓ Unsafe query rejected: {query[:30]}...")
            else:
                print(f"✗ Unsafe query allowed: {query[:30]}...")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ SQL validation test failed: {e}")
        return False

def main():
    """Run all Windows compatibility tests."""
    print("=== Windows Compatibility Test Suite ===")
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Working directory: {os.getcwd()}")
    
    tests = [
        ("Event Loop Policy", test_event_loop_policy),
        ("Module Imports", test_imports),
        ("Server Initialization", test_server_initialization),
        ("Database URL Parsing", test_database_url_parsing),
        ("SQL Validation", test_safe_sql_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = asyncio.run(test_func())
            else:
                result = test_func()
                
            if result:
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
                
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("The server should work correctly on Windows.")
        print("\nYou can now try:")
        print('python -m minimal_postgres_mcp.server "your_database_url" --access-mode restricted')
    else:
        print(f"\n❌ {total - passed} TESTS FAILED")
        print("Please fix the issues before running the server.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
